# Repository Structure Documentation

## Overview

This monorepo is in transition, adapting better architecture patterns and software tooling. The codebase is organized into different categories based on their current status and intended use.

## 🚫 Legacy/Stashed Code Areas (AI Analysis Exclusion)

These directories contain old code that has been stashed during the architectural transition. **AI analysis tools should exclude these areas** and require explicit confirmation before interacting with them.

### Workspace Stashed Code not in Repo (gitignored)
- `apps/__Cherries__` - git play stuff stored here
- `apps/_FUTURE_` - junk drawer (future is now better stored in docs)
- `tooling-build-logs-old` - old logs from before
- `gen-scripts` - dev scripts not needed in repo (yet)
- `packages/db` - front end attempt to make drizzle the SOT source of truth

### Existing Legacy Areas
- `apps/trans_api` - Legacy API implementation
- `apps/websocket-server` - Legacy WebSocket server
- `apps/playwright_docker` - Legacy Playwright testing setup

## ✨ Active Development Areas ("Magic Lego Blocks")

These are the current active development areas where the new architecture is being constructed:

### Applications
- `apps/discord-trans` - Discord integration service
- `apps/web` - Next.js web application
- `apps/celery_worker` - Celery worker service

### Packages & Tooling
- `packages/tooling` - Core tooling scripts and utilities
- `packages/tooling-trans_api` - API-specific tooling
- `packages/tooling-web` - Web-specific tooling
- `packages/trans-db` - Database management package & SOT source of truth
- `packages/tran-hits` - Transcript processing library (moved from apps)
- `packages/discord-trans-db` - Independent Discord bot database
- `packages/ui` - Shared UI components (shadcn)
- `packages/eslint-config` - ESLint configurations
- `packages/typescript-config` - TypeScript configurations
- `packages/test_share` - Shared testing utilities

### Root Level Active Files
- `Makefile` - Build and development commands
- `repo-scripts/` - Repository management scripts
- `turbo.json` - Turborepo configuration
- `pnpm-workspace.yaml` - PNPM workspace configuration

## 📁 Current Repository Structure

Note: 
- Ideas for making labels for commit message styles
- Lots off issues are popping out of this so lets capture them.

```
knowtrails/
├── apps/
│   ├── celery_worker/         ✅ Active - bad shape - could be in package dir)
│   ├── discord-trans/         ✅ Active  
│   ├── playwright_docker/     💡  Future - for testing web .. note zooka nooka online colab playwrite thing i saw
│   ├── tran-hits/             ✅ Moved to packages/tran-hits
│   ├── trans_api/             🚫 Legacy
│   ├── web/                   ✅ Active - Nextjs stuff - waiting for stable backend ... 
│   └── websocket-server/      💡  Future - perhaps after polling works (or instead of)
├── packages/
│   ├── discord-trans-db/      ✅ Active - Independent Discord bot database
│   ├── eslint-config/         ✅ Active
│   ├── test_share/            ✅ Active
│   ├── tooling/               ✅ Active
│   ├── tooling-trans_api/     ✅ Active
│   ├── tooling-web/           ✅ Active
│   ├── tran-hits/             ✅ Active - Transcript processing library (moved from apps)
│   ├── trans-db/              ✅ Active
│   ├── typescript-config/     ✅ Active
│   └── ui/                    ✅ Active
├── repo-scripts/              ✅ Active
├── scripts/                   ✅ Active
├── Makefile                   ✅ Active
└── turbo.json                 ✅ Active
```

## 🤖 AI Tool Guidelines

### For AI Analysis Tools:
1. **EXCLUDE** all directories marked with 🚫 Legacy or 💡 Future
2. **REQUIRE EXPLICIT CONFIRMATION** before interacting with excluded code
3. **FOCUS** on directories marked with ✅ Active for analysis and suggestions
4. **ASK FOR PERMISSION** before making changes to any legacy areas

### For Development:
1. New features should be built in the **Active Development Areas**
2. Legacy code should only be modified with explicit intent to migrate or remove
3. When in doubt about a directory's status, refer to this document

## 🔄 Migration Status

This repository is actively being refactored from legacy patterns to modern architecture. The goal is to make software development more manageable and enjoyable through better tooling and patterns.

### Next Steps:
- [ ] Complete migration from legacy `trans_api` script to new python architecture using hatch and uv (pip for editables)
- [ ] db work in `trans-db` making the new fallacy cards and hooking the meta layers into the db to allow categorization of a unique spinozian kind. SQLalchemy and alembic.
- [ ] Consider `websocket-server` to replace the log polling we have yet to implement in `web`.
- [ ] More future testing ideas `playwright_docker` for `web`
- [ ] Fast api self generates documentation for the API
- [ ] Establish clear deployment pipelines for active services

---

*Last Updated: 2025-06-18*
*This document should be updated as the repository structure evolves.*
