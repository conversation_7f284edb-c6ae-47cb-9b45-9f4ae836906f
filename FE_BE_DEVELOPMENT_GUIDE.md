# Frontend/Backend Parallel Development Guide

**Date**: 2025-06-21  
**Purpose**: Enable separate development in different VSCode editors with clear contracts and handoff points

## 🎯 **Development Philosophy**

### **Paraconsistent FE/BE Alignment**
1. **Story** - Both teams know the official narrative and alternatives
2. **Trust** - Checks/balances with contract validation
3. **Honesty** - Science-minded approach to API design
4. **Humanity** - Kindness by default in team communication
5. **Appreciation** - Wonder/beauty focus in user experience

## 🏗️ **Architecture Overview**

```mermaid
graph TB
    subgraph "Frontend (VSCode 1)"
        FE[Next.js App]
        MOCK[Mock API Layer]
        TYPES[TypeScript Contracts]
    end
    
    subgraph "Contract Bridge"
        API_SPEC[API Specification]
        VALIDATION[Runtime Validation]
        TESTS[Integration Tests]
    end
    
    subgraph "Backend (VSCode 2)"
        BE[FastAPI + Celery]
        MODELS[Pydantic Models]
        DB[Database Services]
    end
    
    FE --> MOCK
    FE --> API_SPEC
    API_SPEC --> BE
    TYPES <--> MODELS
    VALIDATION --> TESTS
```

## 🔧 **Development Environment Setup**

### **Frontend Developer (VSCode 1)**

#### **Quick Start**
```bash
# Terminal 1: Frontend Development
cd apps/web
pnpm install
pnpm dev  # http://localhost:3006

# Enable mock API for independent development
export NEXT_PUBLIC_USE_MOCK_API=true
```

#### **Key Files**
- `apps/web/app/types/index.ts` - TypeScript contracts (SOURCE OF TRUTH)
- `apps/web/app/lib/mock-api.ts` - Mock API responses
- `apps/web/app/components/` - UI components
- `apps/web/app/lib/api-client.ts` - Real API client (when ready)

#### **Development Workflow**
1. **Design UI components** using TypeScript contracts
2. **Use mock API** for data during development
3. **Build features** without waiting for backend
4. **Switch to real API** when backend endpoints are ready

### **Backend Developer (VSCode 2)**

#### **Quick Start**
```bash
# Terminal 2: Backend Development
cd apps/trans_api
source /path/to/venv/bin/activate
pip install -e .
uvicorn src.app:app --reload --port 8080

# Terminal 3: Celery Worker
cd apps/celery_worker
celery -A worker worker --loglevel=info

# Terminal 4: Redis (if not containerized)
redis-server
```

#### **Key Files**
- `apps/trans_api/src/trans_api/models/api_contracts.py` - Pydantic models (MATCHES TypeScript)
- `apps/trans_api/src/trans_api/versions/v1/routers/` - API endpoints
- `apps/trans_api/src/trans_api/services/` - Business logic
- `packages/trans-db/` - Database models and services

#### **Development Workflow**
1. **Implement API endpoints** using Pydantic contracts
2. **Add runtime validation** for request/response
3. **Test with real data** and database operations
4. **Document any contract changes** for frontend team

## 📋 **Contract Management**

### **Contract Update Process**
1. **Propose changes** in shared documentation
2. **Update TypeScript types** first (frontend input)
3. **Update Pydantic models** to match (backend implementation)
4. **Update mock API** to reflect changes
5. **Test integration** when both sides are ready

### **Contract Validation**
```typescript
// Frontend: Runtime type checking
import { z } from 'zod';
import { UserClipTrail } from './types';

const validateTrail = (data: unknown): UserClipTrail => {
  // Runtime validation against TypeScript types
  return TrailSchema.parse(data);
};
```

```python
# Backend: Pydantic validation
from pydantic import ValidationError
from .models.api_contracts import UserClipTrail

def create_trail(data: dict) -> UserClipTrail:
    try:
        return UserClipTrail(**data)
    except ValidationError as e:
        raise HTTPException(status_code=422, detail=e.errors())
```

## 🔄 **Integration Points**

### **Handoff Checklist**
- [ ] **Contract alignment** - TypeScript and Pydantic models match
- [ ] **Mock API updated** - Frontend can develop independently
- [ ] **Endpoint implemented** - Backend provides real functionality
- [ ] **Validation added** - Runtime checks on both sides
- [ ] **Integration tested** - End-to-end functionality verified

### **Communication Protocol**
1. **Daily sync** - 15 min standup on contract changes
2. **Contract changes** - Documented in shared file
3. **Breaking changes** - Coordinated deployment
4. **Integration testing** - Weekly full-stack testing

## 🧪 **Testing Strategy**

### **Frontend Testing**
```bash
# Component testing with mock data
cd apps/web
pnpm test

# E2E testing with mock API
pnpm test:e2e
```

### **Backend Testing**
```bash
# API endpoint testing
cd apps/trans_api
pytest tests/

# Contract validation testing
pytest tests/test_contracts.py
```

### **Integration Testing**
```bash
# Full-stack testing (when both ready)
cd apps/web
NEXT_PUBLIC_USE_MOCK_API=false pnpm test:integration
```

## 📊 **Progress Tracking**

### **Frontend Milestones**
- [ ] User profile components with wisdom dials
- [ ] Trail creation and editing interface
- [ ] Clip annotation and sequencing
- [ ] Social features (likes, forks, sharing)
- [ ] Search and discovery interface

### **Backend Milestones**
- [ ] User profile API endpoints
- [ ] Trail CRUD operations
- [ ] Clip extraction and processing
- [ ] Wisdom integration (opinions, fallacies)
- [ ] Search and recommendation engine

### **Integration Milestones**
- [ ] User authentication flow
- [ ] Trail creation end-to-end
- [ ] Real-time updates (if needed)
- [ ] Performance optimization
- [ ] Production deployment

## 🚀 **Deployment Strategy**

### **Independent Deployment**
- **Frontend**: Vercel/Netlify with mock API
- **Backend**: Docker containers with health checks
- **Integration**: Staged rollout with feature flags

### **Environment Configuration**
```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_USE_MOCK_API=false

# Backend (.env)
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
CELERY_BROKER_URL=redis://...
```

## 🎯 **Success Criteria**

### **Development Velocity**
- Frontend and backend can develop in parallel
- Contract changes are communicated quickly
- Integration happens smoothly when ready

### **Code Quality**
- Type safety maintained across the stack
- Runtime validation prevents contract violations
- Clear separation of concerns

### **Team Collaboration**
- Clear handoff points and responsibilities
- Minimal blocking dependencies
- Effective communication protocols

---

**This guide enables true parallel development while maintaining the paraconsistent FE/BE alignment philosophy. One bridge, two ends, unified vision.**
