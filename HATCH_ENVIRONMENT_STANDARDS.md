# HATCH Environment Layout Standards

**Date:** 2025-06-22 07:55  
**Context:** Standardizing HATCH environment configurations across projects  
**Problem:** Inconsistent environment layouts causing confusion and tooling issues

## 🎯 **Two Valid HATCH Approaches**

### **Approach 1: Separate Environments**
Multiple isolated environments for different purposes.

```toml
[tool.hatch.envs.default]
dependencies = ["base-deps"]

[tool.hatch.envs.default.scripts]
test = "pytest {args}"
dev = "python -m mymodule"

[tool.hatch.envs.discord]
dependencies = ["discord.py", "asyncio-deps"]

[tool.hatch.envs.discord.scripts]
test = "pytest {args}"
start-bot = "python src/bot.py"

[tool.hatch.envs.types]
extra-dependencies = ["mypy>=1.0.0"]

[tool.hatch.envs.types.scripts]
check = "mypy --install-types {args}"
```

**Usage:**
```bash
hatch run test                    # Uses default env
hatch --env discord run test     # Uses discord env
hatch --env types run check      # Uses types env
```

### **Approach 2: Feature-Based (Install Modes)**
Single environment with optional feature dependencies.

```toml
[project.optional-dependencies]
discord = ["discord.py>=2.3.2"]
test = ["pytest>=8.0.0", "pytest-asyncio>=0.21.0"]
dev = ["pytest>=8.0.0", "mypy>=1.0.0"]
types = ["mypy>=1.0.0"]

[tool.hatch.envs.default]
features = ["test", "dev"]

[tool.hatch.envs.default.scripts]
test = "pytest {args}"
test-cov = "pytest --cov=src {args}"
dev = "python -m mymodule"
start-bot = "python src/bot.py"
type-check = "mypy --install-types {args}"
```

**Usage:**
```bash
hatch run test                    # All features available
hatch run start-bot               # Discord features available
hatch run type-check              # Type checking available
```

## 🔍 **Current Project Analysis**

### **apps/discord-trans/pyproject.toml Issues**

❌ **MIXED APPROACH DETECTED:**
```toml
# Separate environments (Approach 1)
[tool.hatch.envs.default]
[tool.hatch.envs.discord]
[tool.hatch.envs.types]

# BUT ALSO feature dependencies (Approach 2)
[project.optional-dependencies]
test = ["pytest>=8.0.0", "pytest-asyncio>=0.21.0"]
discord = ["discord.py>=2.3.2"]
```

This creates **confusion** about which approach is being used.

## 📋 **Standardization Decision**

### **RECOMMENDED: Approach 1 (Separate Environments)**

**Rationale:**
- ✅ **Clear separation** of concerns (discord vs types vs default)
- ✅ **Isolated dependencies** prevent conflicts
- ✅ **Explicit environment selection** with `--env`
- ✅ **Better for complex projects** with multiple runtime contexts

### **Standard Template:**

```toml
[tool.hatch.envs.default]
dependencies = [
    "core-deps-here"
]

[tool.hatch.envs.default.scripts]
# REQUIRED STANDARD COMMANDS
test = "pytest {args}"
test-cov = "pytest --cov=src {args}"
test-v = "pytest -v {args}"

# Project-specific commands
dev = "python -m mymodule"

[tool.hatch.envs.discord]
dependencies = [
    "discord.py>=2.3.2",
    "asyncio-deps"
]

[tool.hatch.envs.discord.scripts]
# REQUIRED STANDARD COMMANDS
test = "pytest {args}"
test-cov = "pytest --cov=src {args}"
test-v = "pytest -v {args}"

# Discord-specific commands
start-bot = "python src/bot.py"
start-unified = "python src/unified_bot.py"

[tool.hatch.envs.types]
extra-dependencies = ["mypy>=1.0.0"]

[tool.hatch.envs.types.scripts]
check = "mypy --install-types --non-interactive {args}"
```

## 🚫 **ANTI-PATTERNS TO AVOID**

### **❌ Mixed Approaches**
```toml
# DON'T mix separate envs with optional-dependencies
[tool.hatch.envs.discord]
[project.optional-dependencies]
discord = ["discord.py"]  # Confusing!
```

### **❌ Inconsistent Script Names**
```toml
# DON'T use different test command names
[tool.hatch.envs.default.scripts]
test = "pytest {args}"

[tool.hatch.envs.discord.scripts]
run-tests = "pytest {args}"  # Should be 'test'!
```

### **❌ Missing Standard Commands**
```toml
# DON'T skip required standard commands
[tool.hatch.envs.default.scripts]
start-app = "python app.py"
# Missing: test, test-cov, test-v
```

## ✅ **ENFORCEMENT RULES**

### **1. Environment Consistency**
- **Choose ONE approach** per project (separate envs OR features)
- **Document the choice** in project README
- **Stick to the choice** throughout the project

### **2. Required Standard Scripts**
Every environment MUST have:
```toml
test = "pytest {args}"
test-cov = "pytest --cov=src {args}"
test-v = "pytest -v {args}"
```

### **3. Environment Naming**
- `default` - Core functionality
- `discord` - Discord bot specific
- `types` - Type checking (mypy)
- `docs` - Documentation generation
- `prod` - Production dependencies

### **4. Command Usage**
```bash
# Standard commands (work in any environment)
hatch run test
hatch --env discord run test
hatch --env types run check

# Environment-specific commands
hatch --env discord run start-bot
hatch --env default run dev
```

## 🔧 **Migration Strategy**

### **For apps/discord-trans:**

1. **Remove mixed approach** - Delete `[project.optional-dependencies]`
2. **Move dependencies** to appropriate environment sections
3. **Standardize script names** across all environments
4. **Test all environments** work independently

### **Example Fix:**
```toml
# BEFORE (mixed)
[project.optional-dependencies]
discord = ["discord.py>=2.3.2"]

[tool.hatch.envs.discord]

# AFTER (clean)
[tool.hatch.envs.discord]
dependencies = ["discord.py>=2.3.2"]
```

## 📊 **Benefits of Standardization**

- ✅ **Predictable commands** across all projects
- ✅ **Clear environment separation** 
- ✅ **Easier debugging** when issues arise
- ✅ **Consistent AI agent behavior**
- ✅ **Better onboarding** for new developers

---

**This standard eliminates the confusion between HATCH environment approaches and ensures consistent, predictable tooling across all projects.**
