# Discord Bot Development - Next Session Prompt
## June 19, 2025 → Next Session

![alt text](image.png)

## 🎯 **Mission: Complete Discord Bot Game Interface**

You are continuing development of a Discord bot with fallacy card collection game mechanics. The database foundation is **COMPLETE** and all systems are **OPERATIONAL**.

## ✅ **Current Status - FOUNDATION COMPLETE**

### **What's Already Built & Tested**:
- ✅ **Independent Discord bot database** (`packages/discord-trans-db`)
- ✅ **Complete SQLAlchemy 2.0 models** with game mechanics
- ✅ **7/7 tests passing** - All models validated
- ✅ **Alembic migration applied** - Database schema ready (`75f97739bc0c`)
- ✅ **Package structure cleaned** - `tran-hits` moved to `/packages`
- ✅ **All imports working** - No path manipulation needed
- ✅ **Documentation complete** - SQLAlchemy patterns documented

### **Database Models Ready**:
1. `DiscordUser` - User progression (skill_level, unlock_level, experience_points)
2. `FallacyCard` - Logical fallacies with rarity system
3. `ClaimCard` - User claims with meta-levels 0-5
4. `FallacyExample` - Contextual examples
5. `ClaimEvidence` - Supporting/refuting evidence
6. `CachedTranscript` - Transcript caching
7. `SearchHistory` - User search tracking
8. Association tables for relationships

## 🎮 **IMMEDIATE GOAL: Implement Discord Bot Commands**

### **Priority 1: Fallacy Card Commands** (Issue #5)
Implement these Discord commands in `/apps/discord-trans/src/discord_trans/trans_bot.py`:

```python
# Commands to implement:
!fallacy              # Discover random fallacy
!fallacy list         # Show available fallacies by rarity  
!fallacy <name>       # Get details about specific fallacy
!collect <fallacy>    # Add fallacy to user collection
!mycards             # Show user's collected fallacies
!progress            # Show user progression stats
```

### **Technical Requirements**:
- Connect to `discord-trans-db` models using async operations
- Implement user progression (XP, skill levels, unlocks)
- Add rarity system (common, rare, epic, legendary)
- Proper error handling and validation

## 📋 **GitHub Project Status**

### **Epic Issue**: #4 - Complete Discord Bot Architecture Rewrite
- **CRITICAL**: Blocks `drizzle-postgress-SOT` branch merge to main
- **Target**: End of week completion

### **Active Issues**:
- **Issue #5**: 🎮 Implement Discord bot fallacy card commands (HIGH PRIORITY)
- **Issue #6**: 🔧 Add commit hooks for standardized commits (MEDIUM)

### **GitHub URLs**:
- **Issues**: https://github.com/ideatrails/knowtrails/issues
- **Epic #4**: https://github.com/ideatrails/knowtrails/issues/4
- **Commands #5**: https://github.com/ideatrails/knowtrails/issues/5

## 🔧 **Development Environment**

### **Working Directory**: 
```bash
cd /home/<USER>/repo/knowtrails/packages/discord-trans-db
# OR
cd /home/<USER>/repo/knowtrails/apps/discord-trans
```

### **Key Commands**:
```bash
# Run tests
hatch run test tests/test_card_models.py -v

# Check migration status  
hatch run alembic current

# Install packages
hatch run pip install -e ../../packages/tran-hits -e ../../packages/discord-trans-db
```

## 🎯 **Game Mechanics to Implement**

### **User Progression System**:
- **XP System**: Users gain experience for discovering fallacies
- **Skill Levels**: 1-10 skill progression
- **Unlock System**: Higher skill unlocks rarer fallacies
- **Meta-Level Access**: unlock_level determines claim complexity access

### **Card Collection**:
- **Rarity Tiers**: common, rare, epic, legendary
- **Discovery Mechanics**: Random fallacy discovery
- **Collection Tracking**: User's collected fallacies
- **Progress Stats**: Total found, XP, current level

## 📚 **Key Documentation**:
- **SQLAlchemy Patterns**: `/home/<USER>/repo/knowtrails_docs/docs/sqlalchemy-schema-design.md`
- **Progress Log**: `discord-bot-final-summary-2025-06-19.md`

## 🚨 **Important Notes**:
- **Branch**: Currently on `drizzle-postgress-SOT` 
- **Database**: Using SQLite for development, PostgreSQL for production
- **Testing**: All changes must maintain 7/7 test passing status
- **Commits**: Use conventional commit format when ready

## 🎯 **Success Criteria for This Session**:
1. **Discord commands functional** - Users can discover and collect fallacies
2. **Database integration working** - Async operations with models
3. **User progression implemented** - XP and skill level tracking
4. **Tests still passing** - No regressions introduced
5. **Issue #5 completed** - Ready for code review

---

**You are the "overtime guy" taking over a solid foundation. Everything is tested and ready - just implement the game interface! 🎮**
