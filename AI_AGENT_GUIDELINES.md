# AI Agent Guidelines for KnowTrails Repository

## 🎯 Purpose

This document provides explicit guidelines for AI analysis tools and agents working with the KnowTrails monorepo during its architectural transition phase.

## 🚫 EXCLUSION ZONES - Legacy/Stashed Code

The following areas contain old code that has been stashed during architectural transition. **AI agents MUST:**

1. **EXCLUDE** these areas from automatic analysis
2. **REQUIRE EXPLICIT CONFIRMATION** before any interaction
3. **ASK FOR PERMISSION** before making any changes

### Legacy Code Areas:

#### Existing Directories:
- `apps/trans_api/` - Legacy API implementation
- `apps/websocket-server/` - Legacy WebSocket server  
- `apps/playwright_docker/` - Legacy Playwright testing setup

#### Future/Planned Legacy Areas:
- `apps/__Cherries__/` *(not currently present)*
- `apps/_FUTURE_/` *(not currently present)*
- `packages/db/` *(not currently present)*
- `tooling-build-logs-old/` *(not currently present)*
- `gen-scripts/` *(not currently present)*

## ✅ ACTIVE DEVELOPMENT ZONES - "Magic Lego Blocks"

These areas are actively being developed and are safe for AI analysis and interaction:

### Applications:
- `apps/discord-trans/` - Discord integration service
- `apps/tran-hits/` - Transaction hits service  
- `apps/web/` - Next.js web application
- `apps/celery_worker/` - Celery worker service

### Packages:
- `packages/tooling/` - Core tooling scripts
- `packages/tooling-trans_api/` - API-specific tooling
- `packages/tooling-web/` - Web-specific tooling
- `packages/trans-db/` - Database management
- `packages/ui/` - Shared UI components
- `packages/eslint-config/` - ESLint configurations
- `packages/typescript-config/` - TypeScript configurations
- `packages/test_share/` - Shared testing utilities

### Root Level:
- `Makefile` - Build and development commands
- `repo-scripts/` - Repository management scripts
- `turbo.json` - Turborepo configuration
- `pnpm-workspace.yaml` - PNPM workspace configuration

## 🤖 AI Agent Behavior Rules

### BEFORE Taking Any Action:

1. **Check Exclusion Status**: Use the provided `.aiignore` file or run:
   ```bash
   ./repo-scripts/check-ai-exclusion.sh <path>
   ```

2. **Confirm Path Status**: If unsure about a path, refer to `REPO_STRUCTURE.md`

3. **Ask for Permission**: When working with legacy areas, always ask:
   > "I notice `<path>` is marked as legacy/stashed code. Do you want me to proceed with explicit confirmation?"

### WHEN Analyzing Code:

- **Focus on Active Areas**: Prioritize analysis of ✅ Active Development zones
- **Ignore Legacy Areas**: Skip 🚫 Legacy areas unless explicitly requested
- **Respect Architecture**: Understand this is a transition period - suggest improvements that align with the new architecture

### WHEN Making Suggestions:

- **Prefer Active Areas**: Suggest changes in active development zones
- **Migration Awareness**: If suggesting changes to legacy code, mention migration implications
- **Ask About Intent**: When unclear, ask if the goal is to improve legacy code or migrate to new architecture

## 🛠️ Tools and Files

### Reference Files:
- `REPO_STRUCTURE.md` - Detailed repository structure documentation
- `.aiignore` - Automated exclusion patterns for AI tools
- `AI_AGENT_GUIDELINES.md` - This file

### Utility Scripts:
- `repo-scripts/check-ai-exclusion.sh` - Check if a path should be excluded

## 🎉 Development Philosophy

Remember: The goal is to make software development **more manageable and fun** 🎉😄🤹‍♂️🎲🍭🧠✨

- **Embrace the Transition**: This is a sandbox for building better architecture
- **Respect the Process**: Legacy code exists for a reason, but new code should follow new patterns  
- **Ask When Uncertain**: Better to ask than to make assumptions about legacy vs. active code
- **Focus on the Future**: Help build the "Magic Lego Blocks" that will make development more enjoyable

---

*This document should be consulted by all AI agents before interacting with the repository.*
