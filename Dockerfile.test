FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy the entire project
COPY . .

# Install uv and hatch
RUN pip install uv hatch

# Test each package in dependency order
# 1. Test tran-hits (no dependencies)
WORKDIR /app/packages/tran-hits
RUN echo "Testing tran-hits package..." && hatch run test:test

# 2. Test discord-trans-db (no dependencies)  
WORKDIR /app/packages/discord-trans-db
RUN echo "Testing discord-trans-db package..." && hatch run test

# 3. Test discord-trans (depends on both above)
WORKDIR /app/apps/discord-trans
RUN echo "Installing discord-trans dependencies..." && hatch run dev-editable
RUN echo "Testing discord-trans app..." && hatch run test

# Success message
RUN echo "🎉 All tests passed in Docker container!"
