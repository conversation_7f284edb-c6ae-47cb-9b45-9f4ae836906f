services:
  db_test:
    image: postgres:13
    environment:
      POSTGRES_DB: test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432" # Use a different port to avoid conflict with default PostgreSQL
    volumes:
      - db_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d test_db"]

      interval: 5s
      timeout: 5s
      retries: 5

  redis_test:
    image: redis:6-alpine
    ports:
      - "6380:6379" # Use a different port to avoid conflict with default Redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  trans_api_test:
    build:
      context: . # Set build context to the repository root
      dockerfile: apps/trans_api/Dockerfile # Specify the Dockerfile path relative to the new context
      target: dev-runner # Use dev-runner to get test dependencies and files
    depends_on:
      db_test:
        condition: service_healthy
      redis_test:
        condition: service_healthy
    environment:
      # Explicitly set DATABASE_URL for the test database connection
      DATABASE_URL: postgresql+asyncpg://test_user:test_password@db_test:5432/test_db
      REDIS_HOST: redis_test
      REDIS_PORT: 6379
      # Set TESTING=true to ensure the application uses the test database configuration
      TESTING: "true"
      # Google YouTube API Credentials for tests
      GOOGLE_YT_CRED: /app/configs/yttlogin-02d2f39f7810.json
      # Celery Broker and Backend URLs for tests
      CELERY_BROKER_URL: redis://redis_test:6379/0
      CELERY_RESULT_BACKEND: redis://redis_test:6379/0
    volumes:
      - ./apps/trans_api:/app/trans_api # Mount the app code for easier development
      - ./tests/trans_api:/app/tests/trans_api # Mount tests for live changes
    # command: ["/bin/bash", "-c", "export REDIS_HOST=redis_test && export REDIS_PORT=6379 && echo 'REDIS_HOST is: '$REDIS_HOST', REDIS_PORT is: '$REDIS_PORT && PYTHONPATH=/app:/app/trans_api pytest tests/trans_api"] # Explicitly set PYTHONPATH for pytest
    # command: ["CMD-SHELL", "PYTHONPATH=/app:/app/trans_api pytest tests/trans_api"] # Explicitly set PYTHONPATH for pytest
    # command: >
    #   /bin/bash -c "echo REDIS_HOST is: $REDIS_HOST, REDIS_PORT is: $REDIS_PORT && PYTHONPATH=/app:/app/trans_api pytest tests/trans_api"
    command:
      - /bin/bash
      - -c
      - |
        echo "=== ENV DUMP ==="
        printenv | grep REDIS
        echo "=== TEST START ==="
        PYTHONPATH=/app:/app/trans_api:/apps pytest -v -s --tb=long tests/trans_api
    # echo "REDIS_HOST is: \$REDIS_HOST, REDIS_PORT is: \$REDIS_PORT"

volumes:
  db_test_data:
