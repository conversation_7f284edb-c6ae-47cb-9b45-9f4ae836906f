from shared_tasks.test_tasks import add
result = add.delay(4, 4)
print(result.get())



[INFO/MainProcess] mingle: searching for neighbors
[CRITICAL/MainProcess] Unrecoverable error: OperationalError('No permissions to access a channel')
-------------- celery@6395f43da040 v5.5.2 (immunity)
--- ***** -----
-- ******* ---- Linux-5.15.153.1-microsoft-standard-WSL2-x86_64-with-glibc2.36 2025-06-03 19:50:20
- *** --- * ---
- ** ---------- [config]
- ** ---------- .> app: worker:0x7f8d999fca40
- ** ---------- .> transport: redis://angels:**@redis-prod:6379//
- ** ---------- .> results: redis://angels:**@redis-prod:6379/
- *** --- * --- .> concurrency: 12 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** -----
-------------- [queues]
.> celery exchange=celery(direct) key=celery
[tasks]
. shared_tasks.test_tasks.add
. shared_tasks.test_tasks.multiply
[2025-06-03 19:50:22,119: INFO/MainProcess] Connected to redis://angels:**@redis-prod:6379//
[2025-06-03 19:50:22,130: INFO/MainProcess] mingle: searching for neighbors
[2025-06-03 19:50:22,154: CRITICAL/MainProcess] Unrecoverable error: OperationalError('No permissions to access a channel')
